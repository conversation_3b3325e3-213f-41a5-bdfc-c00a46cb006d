<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Login</title>

  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>

  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@1.14.0/build/css/alertify.min.css"/>
  <script src="//cdn.jsdelivr.net/npm/alertifyjs@1.14.0/build/alertify.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <link rel="stylesheet" href="{% static 'login.css' %}">

  <style>
     label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}
  </style>
 
</head>
<body>
  <div class="login-wrapper">
    <div class="login-left">
      <h1 class="d-3 fw-bold text-dark ">Does anyone knows your password. shh!!</h1>
      <p class="text-dark ">We got your back.</p>
      <img src="{% static 'images/download (13).png' %}" alt="Login Illustration" />
    </div>

    <div class="login-right">
      <form class="form" method="post" action="">
        {% csrf_token %}
        {% for fm in form %}
          {{fm.label_tag}}{{fm}}{{fm.errors|striptags}}
        {% endfor %}
        

        
        <button type="submit" class="btn-submit">Change Password</button>

        

      </form>
    </div>
  </div>


   {% if messages %}

  {% for message in messages %}

  {% if message.tags == 'success' %}
  <script>
    alertify.set('notifier', 'position', 'top-right');
    alertify.success('{{message}}');
  </script>
  {% elif message.tags == 'error' %}
  <script>
    alertify.set('notifier', 'position', 'top-right');
    alertify.error('{{message}}');
  </script>
  {% endif %}

  {% endfor %}

  {% endif %}


</body>
</html>