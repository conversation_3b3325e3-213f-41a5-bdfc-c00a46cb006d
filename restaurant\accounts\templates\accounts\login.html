<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Login</title>

  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>

  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@1.14.0/build/css/alertify.min.css"/>
  <script src="//cdn.jsdelivr.net/npm/alertifyjs@1.14.0/build/alertify.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <link rel="stylesheet" href="{% static 'login.css' %}">
 
</head>
<body>
  
  <div class="login-wrapper">
    <div class="login-left">
      <h1 class="d-3 fw-bold text-dark ">Welcome To E-Shopper</h1>
      <p class="text-dark ">Sign in to access and continue exploring our website.</p>
      <img src="{% static 'images/download (13).png' %}" alt="Login Illustration" />
    </div>

        <!-- <h3 class="text-center alert alert-info">Welcome to Login<a href="{% url 'index' %}"><i class="fa solid fa-circle-xmark"></i></h3> -->

    <div class="login-right">
      <form class="form" method="post" action="{% url 'login' %}">
        {% csrf_token %}
    <h3 class="text-center alert alert-info">Welcome to Login<a href="{% url 'index' %}">
      <i class="fa solid fa-circle-xmark"></i></a></h3>
        
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" name="username" id="username" required placeholder="Enter your username">
          <input type="hidden" name="next" value="{{next}}">
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" name="password" id="password" required placeholder="Enter your password">
        </div>

        <div>
          <input type="checkbox" name="remember_me">
          <label for="">Remember me</label>
        </div>

        <div class="options">
          
          <a href="{% url 'password_reset' %}">Forgot password?</a>
        </div>

        <button type="submit" class="btn-submit">Sign In</button>

        <p class="divider">Or sign in with</p>

        <div class="social-buttons">
          <a href="{% url 'social:begin' 'google-oauth2' %}" class="social google">Google</a>
          <a href="{% url 'social:begin' 'github' %}" class="social github">GitHub</a>
        </div>

        <p class="signup-link">Don't have an account? <a href="">Sign Up</a></p>
      </form>
    </div>
  </div>

  {% if messages %}

  {% for message in messages %}

  {% if message.tags == 'success' %}

  <script>
    alertify.set('notifier','position', 'top-right');
    alertify.success('{{message}}');
  </script>

  {%elif message.tags == 'error' %}

 <script>
    alertify.set('notifier','position', 'top-right');
    alertify.error('{{message}}');
  </script>

  {% endif %}

  {% endfor %}

  {% endif %}

</body>
</html>

