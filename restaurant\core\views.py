from django.shortcuts import render, redirect
from .models import Contact
from django.contrib import messages
from django.core.mail import send_mail
from django.template.loader import render_to_string
from datetime import datetime
from .models import Category, Momo

from django.contrib.auth.decorators import login_required

# Create your views here.
def index(request):
    category=Category.objects.all()
    cateid=request.GET.get('category')
    if cateid:
        momo=Momo.objects.filter(category=cateid)
    else:
        momo=Momo.objects.all()
    
    if request.method == "POST":
        name = request.POST['name']
        email = request.POST['email']
        phone = request.POST['phone']
        message = request.POST['message']

        Contact.objects.create(name=name, email=email, phone=phone, message=message)
        
        subject="django training"
        message=render_to_string('core/msg.html',{'name':name})
        from_email="<EMAIL>"
        recipiet_list=[email]
        
        send_mail(subject,message,from_email,recipiet_list,fail_silently=True)
        
        messages.success(request,"Your from has been submitted successfully.")
        return redirect('index')
    
    context={
        'date':datetime.now(),
        'category':category,
        'momo':momo 
    }
    
    return render(request, 'core/index.html',context)

@login_required(login_url='login')
def about(request):
    return render(request, 'core/about.html')

@login_required(login_url='login')
def menu(request):
    return render(request, 'core/menu.html')

@login_required(login_url='login')
def services(request):
    return render(request, 'core/services.html')

@login_required(login_url='login')
def contact(request):
    return render(request, 'core/contact.html')
