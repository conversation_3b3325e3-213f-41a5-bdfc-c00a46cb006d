{% extends 'profile_dashboard.html' %}
{% load static %}

{% block title %}My Profile{% endblock %}
{% block page_title %}Profile{% endblock %}
{% block page_subtitle %}Manage your account information{% endblock %}

{% block maincontent %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header p-3 pb-0">
                <h6>Profile Information</h6>
                <p class="text-sm mb-0">Update your account's profile information and email address.</p>
            </div>
            <div class="card-body p-3">
                <div class="row">
                    <!-- Profile Picture Column -->
                    <div class="col-md-4 text-center">
                        <div class="position-relative">
                           
                            {% if request.user.profile.profile_picture %}
                                <img src="{{request.user.profile.profile_picture.url}}" 
                                     class="avatar avatar-xxl rounded-circle shadow-lg"
                                     alt="Default Profile">

                            {% else %}
                                <img src="{% static 'images/profile.jpg' %}" 
                                     class="avatar avatar-xxl rounded-circle shadow-lg"
                                     alt="Default Profile">
                            {% endif%}
                           
                            <button class="btn btn-sm btn-icon-only bg-gradient-primary position-absolute bottom-0 end-0 mb-n2 me-n2 rounded-circle" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#changePictureModal">
                                <i class="fas fa-pen text-white" aria-hidden="true"></i>
                            </button>
                        </div>
                        <h5 class="mt-3 mb-1">{{request.user.first_name}} {{request.user.last_name}}</h5>
                        <p class="text-sm text-muted mb-3">{{request.user.username}} </p>
                        
                        <div class="d-flex justify-content-center gap-2">
                            <button class="btn btn-sm btn-outline-primary mb-0" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                                Edit Profile
                            </button>
                        </div>
                    </div>
                    
                    <!-- Profile Details Column -->
                    <div class="col-md-8">
                        <ul class="list-group">
                            <li class="list-group-item border-0 ps-0 pt-0 text-sm">
                                <strong class="text-dark">Full Name:</strong> &nbsp; 
                                {{request.user.first_name}} {{request.user.last_name}}
                            </li>
                            <li class="list-group-item border-0 ps-0 text-sm">
                                <strong class="text-dark">Email:</strong> &nbsp; 
                                {{request.user.email}}
                                
                                    <span class="badge bg-gradient-warning ms-2">Unverified</span>
                                
                            </li>
                            <li class="list-group-item border-0 ps-0 text-sm">
                                <strong class="text-dark">Phone:</strong> &nbsp; 
                                {{request.user.profile.phone}}
                            </li>
                            <li class="list-group-item border-0 ps-0 text-sm">
                                <strong class="text-dark">Address:</strong> &nbsp; 
                               {{request.user.profile.address}}
                            </li>
                            <li class="list-group-item border-0 ps-0 text-sm">
                                <strong class="text-dark">Member Since:</strong> &nbsp; 
                                {{request.user.date_joined}}
                            </li>
                            <li class="list-group-item border-0 ps-0 text-sm">
                                <strong class="text-dark">Last Login:</strong> &nbsp; 
                               {{request.user.last_login}}

                            </li>
                        </ul>
                        
                        <!-- Bio Section -->
                        <div class="mt-4">
                            <h6 class="mb-1">About Me</h6>
                            <p class="text-sm">
                                {{request.user.profile.bio}}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Additional Profile Cards -->
        <div class="row">
            <!-- Account Security -->
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header p-3 pb-0">
                        <h6>Account Security</h6>
                        <p class="text-sm mb-0">Manage your password and security settings</p>
                    </div>
                    <div class="card-body p-3">
                        <button class="btn btn-sm bg-gradient-dark mb-0" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            Change Password
                        </button>
                        
                    </div>
                </div>
            </div>
            
           
            
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1" role="dialog" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">Edit Profile</h5>
                <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token%}
                
                <div class="modal-body">
                  {{form}}
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn bg-gradient-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn bg-gradient-primary">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" role="dialog" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">Change Password</h5>
                <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <!-- Password change form fields would go here -->
                    <div class="form-group">
                        <label for="current_password">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="form-group">
                        <label for="new_password">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn bg-gradient-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn bg-gradient-primary">Update Password</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Any profile-specific JavaScript can go here
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}