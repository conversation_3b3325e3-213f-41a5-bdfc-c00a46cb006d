body {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(to bottom,#Fff,#2f9eff);
    display: flex;
    justify-content: left;
    align-items: left;
    height: 100vh;
}

.register-container {
    background: linear-gradient(to bottom,#Fff,#2f9eff);
    padding: 60px 80px;
    border-radius: 16px;
    /* box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); */
    max-width: 700px;
    width: 100%;
   
    
}

.register-form {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: space-between;
}

.form-title {
    width: 100%;
    font-size: 32px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 40px;
    color: #111;
}

.form-group {
    position: relative;
    flex: 1 1 45%;
    min-width: 200px;
}

.form-group input {
    width: 100%;
    padding: 14px;
    border: 1px solid #ccc;
    border-radius: 8px;
    background: #ffffff;
    font-size: 16px;
    transition: border 0.3s;
}

.form-group input:focus {
    border-color: #000;
    background: #fff;
    outline: none;
}

.form-group label {
    position: absolute;
    top: 14px;
    left: 16px;
    background-color: #ffffff;
    padding: 0 6px;
    font-size: 14px;
    color: #777;
    transition: 0.3s;
    pointer-events: none;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label {
    top: -10px;
    left: 12px;
    font-size: 12px;
    background: #ffffff;
    color: #333;
}

.btn-submit {
    width: 100%;
    background-color: #111;
    color: #fff;
    padding: 14px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s;
    margin-top: 10px;
}

.btn-submit:hover {
    background-color: #333;
}

.login-link {
    width: 100%;
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #555;
}

.login-link a {
    color: #000;
    font-weight: 500;
    text-decoration: none;
}

.login-link a:hover {
    text-decoration: underline;
}
