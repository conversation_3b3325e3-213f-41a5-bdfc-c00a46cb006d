from django.contrib import admin
from django.utils.html import format_html
from .models import *
admin.site.site_header="Myshop project"
admin.site.site_title="Myshop project"
admin.site.index_title="Sipalaya"

# Register your models here.
admin.site.register(Category)

@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_items', 'total_price', 'created_at']
    list_filter = ['created_at']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    list_display = ['cart', 'momo', 'quantity', 'get_total_price', 'added_at']
    list_filter = ['added_at', 'momo__category']

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'user', 'total_amount', 'status', 'payment_status', 'created_at']
    list_filter = ['status', 'payment_status', 'created_at']
    search_fields = ['order_number', 'user__username', 'user__email']
    readonly_fields = ['order_number', 'created_at', 'updated_at']
    list_editable = ['status', 'payment_status']

@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ['order', 'momo', 'quantity', 'price', 'get_total_price']
    list_filter = ['order__created_at', 'momo__category']

@admin.register(Wishlist)
class WishlistAdmin(admin.ModelAdmin):
    list_display = ['user', 'created_at']
    filter_horizontal = ['items']

@admin.register(Momo)
class MomoAdmin(admin.ModelAdmin):
    list_display = ['id','name','category','price','stock_quantity','is_available','display_image']
    list_display_links = ['name']
    list_editable = ['category','price','stock_quantity','is_available']
    list_filter = ['category','price','is_available','created_at']
    list_per_page=10
    ordering=['name']
    search_fields = ['name', 'desc']
    readonly_fields = ['created_at', 'updated_at']
    
    def display_image(self,obj):
        if obj.image:
            return format_html('<img src="{}" width="100px" height="100px"; >',obj.image.url)
       