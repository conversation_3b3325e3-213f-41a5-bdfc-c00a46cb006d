body {
  margin: 0;
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #337bff, #43e9ff);
}

.login-wrapper {
  display: flex;
  height: 100vh;
}

.login-left {
  flex: 1;
  background: linear-gradient(to top,#Fff,#2f9eff);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  text-align: center;
}

.login-left h1 {
  font-size: 48px;
  margin-bottom: 10px;
}

.login-left p {
  font-size: 18px;
  margin-bottom: 30px;
}

.login-left img {
  max-width: 80%;
  height: auto;
}

.login-right {
  flex: 1;
  background: linear-gradient(to top,#Fff,#2f9eff);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px;
}

.form {
  width: 100%;
  height: 100%;
  max-width: 500px;
  background: linear-gradient(to top,#Fff,#2f9eff);
  padding: 30px;
  border-radius: 15px; 
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.form h2 {
  margin-bottom: 25px;
  color: #12372A;
  font-weight: bold;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 14px;
}

.options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  margin-bottom: 20px;
}

.options a {
  color: #2d79f3;
  text-decoration: none;
}

.btn-submit {
  width: 100%;
  padding: 12px;
  background-color: #12372A;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  transition: background 0.3s ease;
}

.btn-submit:hover {
  background-color: #0f2d22;
}

.divider {
  text-align: center;
  margin: 20px 0;
  color: #888;
}

.social-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.social {
  padding: 10px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  text-decoration: none;
  transition: background 0.3s;
}

.social.google {
  background-color: #9c0606;
}

.social.github {
  background-color: #333;
}

.social:hover {
  opacity: 0.9;
}

.signup-link {
  margin-top: 20px;
  text-align: center;
  font-size: 14px;
}

.signup-link a {
  color: #2d79f3;
  font-weight: 500;
  text-decoration: none;
}
