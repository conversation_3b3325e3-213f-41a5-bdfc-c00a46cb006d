from django.urls import path
from .views import *
from django.contrib.auth import views as auth_views

urlpatterns = [
    path("register/",register,name="register"),
    path("login/",log_in,name="login"),
    path("logout/",log_out,name="logout"),
    path("change_pass/",change_pass,name="change_pass"),
    path('password_reset/', auth_views.PasswordResetView.as_view(template_name='accounts/pass_reset.html'), name='password_reset'),
    path('password_reset_done/', auth_views.PasswordResetDoneView.as_view(template_name='accounts/change_pass.html'), name='password_reset_done'),
    path('password_reset_confirm/<uidb64>/<token>/',auth_views.PasswordResetConfirmView.as_view(template_name='confirm_pass.html'), name='password_reset_confirm'),
    path('password_reset_complete/', auth_views.PasswordResetCompleteView.as_view(), name='password_reset_complete'),
    path('profile_dashboard/', profile_dashboard, name='profile_dashboard'),
    path('profile/', profile, name='profile'),
    
]
