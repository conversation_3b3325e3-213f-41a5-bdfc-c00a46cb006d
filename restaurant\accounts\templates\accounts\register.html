<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register</title>

  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" crossorigin="anonymous" />

  <script src="//cdn.jsdelivr.net/npm/alertifyjs@1.14.0/build/alertify.min.js"></script>
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/alertifyjs@1.14.0/build/css/alertify.min.css"/>

  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;500;600&display=swap" rel="stylesheet">

  <link rel="stylesheet" href="{% static 'register.css' %}">
   
</head>
<body>
  <div class="register-container">
    <h3 class="text-center alert alert-info">Welcome to Register<a href="{% url 'index' %}">
      <i class="fa solid fa-circle-xmark"></i></a></h3>
    <form method="post" class="register-form" action="{% url 'register' %}">
     
      {% csrf_token %}

      <div class="form-group">
        <input type="text" name="first_name" id="first_name" placeholder=" " required />
        <label for="first_name">First Name</label>
      </div>

      <div class="form-group">
        <input type="text" name="last_name" id="last_name" placeholder=" " required />
        <label for="last_name">Last Name</label>
      </div>

      <div class="form-group">
        <input type="text" name="username" id="username" placeholder=" " required />
        <label for="username">Username</label>
      </div>

      <div class="form-group">
        <input type="email" name="email" id="email" placeholder=" " required />
        <label for="email">Email Address</label>
      </div>


      <div class="form-group">
        <input type="password" name="password" id="password" placeholder=" " required />
        <label for="password">Password</label>
      </div>

      <div class="form-group">
        <input type="password" name="password1" id="password1" placeholder=" " required />
        <label for="password1">Confirm Password</label>
      </div>

      <button type="submit" class="btn-submit">Sign Up</button>

      <p class="login-link">
        Already have an account?
        <a href="">Sign In</a>
      </p>
    </form>
    
  </div>
  <div class="container">
    <div class="row">
      <div class="col-6 mx-auto my-5">
        <h1 ><span class="display-3 fw-bolder mx-auto">Register</span><br> Your Account And Connect With Us.</h1>
        <img src="{% static 'images/person.jpg' %}" height="100%" width="100%" alt="">
      </div>
    </div>
  </div>

  {% if messages %}

  {% for message in messages %}

  {% if message.tags == 'success' %}

  <script>
    alertify.set('notifier','position', 'top-right');
    alertify.success('{{message}}');
  </script>

  {%elif message.tags == 'error' %}

 <script>
    alertify.set('notifier','position', 'top-right');
    alertify.error('{{message}}');
  </script>

  {% endif %}

  {% endfor %}

  {% endif %}

</body>
</html>